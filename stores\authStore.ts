/**
 * Authentication Store - Zustand
 *
 * Centralized authentication state management with:
 * - Token management with expiration
 * - User profile state
 * - Persistent storage
 * - Auto-logout on token expiry
 * - Type-safe state management
 */

import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

// Types
export interface User {
  id: string;
  email: string;
  name: string;
  role: 'ADMIN' | 'TEACHER' | 'STUDENT';
  avatar?: string;
  permissions: string[];
}

export interface AuthState {
  // State
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
  refreshToken: string | null;
  tokenExpiry: number | null;
  isLoading: boolean;
  error: string | null;

  // Actions
  login: (credentials: LoginCredentials) => Promise<void>;
  setAuthData: (token: string, user: User, refreshToken?: string) => void;
  logout: () => void;
  refreshAuth: () => Promise<void>;
  updateUser: (user: Partial<User>) => void;
  clearError: () => void;
  checkTokenExpiry: () => boolean;
  setLoading: (loading: boolean) => void;
  initialize: () => void;
}

export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface AuthResponse {
  user: User;
  token: string;
  refreshToken: string;
  expiresIn: number;
}

// Mock authentication function
const mockAuthenticate = async (credentials: LoginCredentials): Promise<AuthResponse> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Demo credentials validation
  if (credentials.email === '<EMAIL>' && credentials.password === 'admin123') {
    return {
      user: {
        id: '1',
        email: '<EMAIL>',
        name: 'School Administrator',
        role: 'ADMIN',
        avatar: 'https://api.dicebear.com/7.x/initials/svg?seed=School Administrator',
        permissions: ['read', 'write', 'delete', 'admin'],
      },
      token: 'mock-jwt-token-' + Date.now(),
      refreshToken: 'mock-refresh-token-' + Date.now(),
      expiresIn: 3600, // 1 hour
    };
  }

  throw new Error('Invalid credentials');
};

// Create the auth store
export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial state
      isAuthenticated: false,
      user: null,
      token: null,
      refreshToken: null,
      tokenExpiry: null,
      isLoading: true, // Start with loading true, will be set to false after initialization
      error: null,

      // Actions
      login: async (credentials: LoginCredentials) => {
        try {
          set({ isLoading: true, error: null });

          const response = await mockAuthenticate(credentials);
          const expiryTime = Date.now() + response.expiresIn * 1000;

          set({
            isAuthenticated: true,
            user: response.user,
            token: response.token,
            refreshToken: response.refreshToken,
            tokenExpiry: expiryTime,
            isLoading: false,
            error: null,
          });
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Login failed',
            isAuthenticated: false,
            user: null,
            token: null,
            refreshToken: null,
            tokenExpiry: null,
          });
          throw error;
        }
      },

      setAuthData: (token: string, user: User, refreshToken?: string) => {
        try {
          // Parse token to get expiry
          const tokenParts = token.split('.');
          let tokenExpiry = null;

          if (tokenParts.length === 3) {
            try {
              const payload = JSON.parse(atob(tokenParts[1]));
              tokenExpiry = payload.exp ? payload.exp * 1000 : null;
            } catch (e) {
              console.warn('Failed to parse token expiry:', e);
            }
          }

          set({
            isAuthenticated: true,
            user,
            token,
            refreshToken: refreshToken || null,
            tokenExpiry,
            isLoading: false,
            error: null,
          });
        } catch (error) {
          console.error('Failed to set auth data:', error);
          set({
            isLoading: false,
            error: 'Failed to set authentication data',
          });
        }
      },

      logout: () => {
        set({
          isAuthenticated: false,
          user: null,
          token: null,
          refreshToken: null,
          tokenExpiry: null,
          error: null,
          isLoading: false,
        });
      },

      refreshAuth: async () => {
        const { refreshToken } = get();
        if (!refreshToken) {
          get().logout();
          return;
        }

        try {
          set({ isLoading: true });

          // Mock refresh logic
          await new Promise(resolve => setTimeout(resolve, 500));

          const newExpiryTime = Date.now() + 3600 * 1000; // 1 hour
          const newToken = 'refreshed-token-' + Date.now();

          set({
            token: newToken,
            tokenExpiry: newExpiryTime,
            isLoading: false,
          });
        } catch (error) {
          if (process.env.NODE_ENV === 'development') {
            console.error('Token refresh failed:', error);
          }
          get().logout();
        }
      },

      updateUser: (userData: Partial<User>) => {
        const { user } = get();
        if (user) {
          set({
            user: { ...user, ...userData },
          });
        }
      },

      clearError: () => {
        set({ error: null });
      },

      checkTokenExpiry: () => {
        const { tokenExpiry, isAuthenticated } = get();

        if (!isAuthenticated || !tokenExpiry) {
          return false;
        }

        const isExpired = Date.now() >= tokenExpiry;

        if (isExpired) {
          get().logout();
          return false;
        }

        // Auto-refresh if token expires in less than 5 minutes
        const fiveMinutes = 5 * 60 * 1000;
        if (tokenExpiry - Date.now() < fiveMinutes) {
          get().refreshAuth();
        }

        return true;
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      initialize: () => {
        // This function is called after hydration to properly initialize auth state
        // For now, just set loading to false since we're using cookie-based auth
        // The middleware will handle the actual authentication check
        set({ isLoading: false });
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: state => ({
        isAuthenticated: state.isAuthenticated,
        user: state.user,
        token: state.token,
        refreshToken: state.refreshToken,
        tokenExpiry: state.tokenExpiry,
      }),
      onRehydrateStorage: () => state => {
        // Initialize auth state after rehydration
        if (state) {
          state.initialize();
        }
      },
    }
  )
);

// Selectors for better performance
export const useAuth = () => {
  const {
    isAuthenticated,
    user,
    isLoading,
    error,
    login,
    logout,
    setAuthData,
    clearError,
    checkTokenExpiry,
    initialize,
  } = useAuthStore();

  return {
    isAuthenticated,
    user,
    isLoading,
    error,
    login,
    logout,
    setAuthData,
    clearError,
    checkTokenExpiry,
    initialize,
  };
};

export const useAuthUser = () => useAuthStore(state => state.user);
export const useAuthToken = () => useAuthStore(state => state.token);
export const useAuthLoading = () => useAuthStore(state => state.isLoading);
export const useAuthError = () => useAuthStore(state => state.error);

// Auth utilities
export const getAuthToken = () => useAuthStore.getState().token;
export const isAuthenticated = () => useAuthStore.getState().isAuthenticated;
export const getCurrentUser = () => useAuthStore.getState().user;
