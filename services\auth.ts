/**
 * Authentication Services
 * 
 * Thin service layer for auth operations.
 * Returns backend types as-is, no transformations.
 */

import { api } from '@/api/apiClient';
import { AUTH_ENDPOINTS } from '@/lib/auth-config';
import type {
  Token,
  LoginPayload,
  User,
  UpdateMePayload,
  ChangePasswordPayload,
  RegisterPayload,
} from '@/types/auth';

/**
 * Login user with credentials
 */
export async function login(payload: LoginPayload): Promise<Token> {
  const response = await api.post<Token>(AUTH_ENDPOINTS.login, payload);
  return response.data;
}

/**
 * Logout current user
 */
export async function logout(): Promise<void> {
  await api.post(AUTH_ENDPOINTS.logout);
}

/**
 * Get current user profile
 */
export async function me(): Promise<User> {
  const response = await api.get<User>(AUTH_ENDPOINTS.me);
  return response.data;
}

/**
 * Update current user profile
 */
export async function updateMe(data: UpdateMePayload): Promise<User> {
  const response = await api.put<User>(AUTH_ENDPOINTS.updateMe, data);
  return response.data;
}

/**
 * Change current user password
 */
export async function changePassword(data: ChangePasswordPayload): Promise<void> {
  await api.post(AUTH_ENDPOINTS.changePassword, data);
}

/**
 * Register new user (only available in users namespace)
 */
export async function register(payload: RegisterPayload): Promise<Token> {
  const response = await api.post<Token>(AUTH_ENDPOINTS.register, payload);
  return response.data;
}
