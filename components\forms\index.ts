/**
 * Forms Barrel Export
 * 
 * Centralized export for all form components demonstrating
 * strict type usage with Zod + React Hook Form
 */

// Form Components
export { TeacherForm } from './TeacherForm';
export { StudentForm } from './StudentForm';
export { ClassForm } from './ClassForm';
export { SubjectForm } from './SubjectForm';

// Form Types
export type { TeacherFormProps } from './TeacherForm';
export type { StudentFormProps } from './StudentForm';
export type { ClassFormProps } from './ClassForm';
export type { SubjectFormProps } from './SubjectForm';

// Re-export form-related schemas and types for convenience
export {
  TeacherCreateSchema,
  TeacherUpdateSchema,
  StudentCreateSchema,
  StudentUpdateSchema,
  ClassCreateSchema,
  ClassUpdateSchema,
  SubjectCreateSchema,
  SubjectUpdateSchema,
  type TeacherCreate,
  type TeacherUpdate,
  type StudentCreate,
  type StudentUpdate,
  type ClassCreate,
  type ClassUpdate,
  type SubjectCreate,
  type SubjectUpdate,
} from '@/schemas/zodSchemas';
