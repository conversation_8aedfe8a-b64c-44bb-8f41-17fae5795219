'use client';

/**
 * Test Students Page - Authentication + API Testing
 *
 * This page provides a simple interface to:
 * 1. Authenticate with the backend
 * 2. Test student API endpoints
 * 3. Debug integration issues
 */

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { useState } from 'react';

export default function TestStudentsPage() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [authToken, setAuthToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [testResults, setTestResults] = useState<string[]>([]);
  const { toast } = useToast();

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const handleLogin = async () => {
    setIsLoading(true);
    addResult('🔐 Attempting authentication...');

    try {
      // Test multiple authentication endpoints
      const endpoints = [
        '/api/auth/login',
        '/api/v1/auth/login', // Direct backend path
      ];

      for (const endpoint of endpoints) {
        addResult(`🔍 Trying endpoint: ${endpoint}`);

        const formData = new URLSearchParams();
        formData.append('username', 'admin');
        formData.append('password', 'admin123');

        const response = await fetch(endpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: formData,
        });

        addResult(`📡 Response status: ${response.status} ${response.statusText}`);
        addResult(
          `📡 Response headers: ${JSON.stringify(Object.fromEntries(response.headers.entries()))}`
        );

        if (response.ok) {
          const data = await response.json();
          addResult(`✅ Authentication successful with ${endpoint}!`);
          addResult(`🔑 Token type: ${data.token_type || 'unknown'}`);
          addResult(
            `🔑 Token preview: ${
              data.access_token ? data.access_token.substring(0, 30) + '...' : 'none'
            }`
          );

          setAuthToken(data.access_token);
          setIsAuthenticated(true);

          toast({
            title: 'Success',
            description: 'Authentication successful!',
          });
          return; // Success, exit the loop
        } else {
          const errorText = await response.text();
          addResult(`❌ ${endpoint} failed: ${response.status} - ${errorText}`);

          // Try to parse error as JSON for better debugging
          try {
            const errorJson = JSON.parse(errorText);
            addResult(`🔍 Error details: ${JSON.stringify(errorJson, null, 2)}`);
          } catch {
            addResult(`🔍 Raw error: ${errorText}`);
          }
        }
      }

      // If we get here, all endpoints failed
      toast({
        title: 'Error',
        description: 'All authentication endpoints failed',
        variant: 'destructive',
      });
    } catch (error: any) {
      addResult(`❌ Authentication error: ${error.message}`);
      addResult(`🔍 Error stack: ${error.stack}`);
      toast({
        title: 'Error',
        description: `Authentication error: ${error.message}`,
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const testGetStudents = async () => {
    if (!authToken) {
      addResult('❌ No auth token available');
      return;
    }

    setIsLoading(true);
    addResult('📋 Testing GET /students...');

    try {
      const response = await fetch('/api/students?page=1&size=5', {
        headers: {
          Authorization: `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      addResult(`📋 GET /students status: ${response.status}`);

      if (response.ok) {
        const data = await response.json();
        addResult(`✅ GET /students successful - Total: ${data.total || 0}`);
        addResult(`📊 Response structure: ${Object.keys(data).join(', ')}`);
      } else {
        const errorText = await response.text();
        addResult(`❌ GET /students failed: ${errorText}`);
      }
    } catch (error: any) {
      addResult(`❌ GET /students error: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testCreateStudent = async () => {
    if (!authToken) {
      addResult('❌ No auth token available');
      return;
    }

    setIsLoading(true);
    addResult('👤 Testing POST /students...');

    try {
      const testStudent = {
        reg_no: `TEST${Date.now()}`,
        first_name: 'Test',
        last_name: 'Student',
        gender: 'male' as const,
        class_id: '1',
        section_id: '1',
        password: 'TestPassword123',
        parent_id: 'test-parent-id',
        email: `test${Date.now()}@school.com`,
        guardian_name: 'Test Guardian',
        guardian_phone: '+1234567890',
        address: '123 Test Street',
      };

      addResult(`👤 Creating student: ${testStudent.first_name} ${testStudent.last_name}`);

      const response = await fetch('/api/students', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testStudent),
      });

      addResult(`👤 POST /students status: ${response.status}`);

      if (response.ok) {
        const data = await response.json();
        addResult(`✅ Student created successfully! ID: ${data.id}`);
        addResult(`📊 Created student: ${data.first_name} ${data.last_name}`);
      } else {
        const errorText = await response.text();
        addResult(`❌ POST /students failed: ${errorText}`);
      }
    } catch (error: any) {
      addResult(`❌ POST /students error: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const testBackendHealth = async () => {
    setIsLoading(true);
    addResult('🏥 Testing backend health...');

    try {
      // Test multiple health endpoints
      const healthEndpoints = [
        '/api/docs',
        '/api/v1/docs',
        'http://127.0.0.1:8000/docs',
        'http://127.0.0.1:8000/api/v1/docs',
      ];

      for (const endpoint of healthEndpoints) {
        try {
          addResult(`🔍 Testing: ${endpoint}`);
          const response = await fetch(endpoint, { method: 'GET' });
          addResult(`📡 ${endpoint}: ${response.status} ${response.statusText}`);

          if (response.ok) {
            addResult(`✅ Backend accessible via ${endpoint}`);
            break;
          }
        } catch (error: any) {
          addResult(`❌ ${endpoint}: ${error.message}`);
        }
      }

      // Test API root
      const apiResponse = await fetch('/api/', { method: 'GET' });
      addResult(`📡 API root (/api/): ${apiResponse.status} ${apiResponse.statusText}`);
    } catch (error: any) {
      addResult(`❌ Health check error: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className='container mx-auto p-6 space-y-6'>
      <h1 className='text-3xl font-bold'>Student API Integration Test</h1>

      {/* Authentication Section */}
      <Card>
        <CardHeader>
          <CardTitle>Authentication</CardTitle>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='flex items-center space-x-4'>
            <div className='flex-1'>
              <Label>Status</Label>
              <div
                className={`p-2 rounded ${
                  isAuthenticated ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}
              >
                {isAuthenticated ? '✅ Authenticated' : '❌ Not Authenticated'}
              </div>
            </div>
            <Button onClick={handleLogin} disabled={isLoading || isAuthenticated} className='mt-6'>
              {isLoading ? 'Authenticating...' : 'Login as Admin'}
            </Button>
          </div>
          {authToken && (
            <div>
              <Label>Token Preview</Label>
              <div className='p-2 bg-gray-100 rounded text-sm font-mono'>
                {authToken.substring(0, 50)}...
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* API Testing Section */}
      <Card>
        <CardHeader>
          <CardTitle>API Testing</CardTitle>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='flex flex-wrap gap-4'>
            <Button onClick={testBackendHealth} disabled={isLoading} variant='outline'>
              Test Backend Health
            </Button>
            <Button
              onClick={testGetStudents}
              disabled={!isAuthenticated || isLoading}
              variant='outline'
            >
              Test GET Students
            </Button>
            <Button
              onClick={testCreateStudent}
              disabled={!isAuthenticated || isLoading}
              variant='outline'
            >
              Test CREATE Student
            </Button>
            <Button onClick={clearResults} variant='outline'>
              Clear Results
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Results Section */}
      <Card>
        <CardHeader>
          <CardTitle>Test Results</CardTitle>
        </CardHeader>
        <CardContent>
          <div className='bg-black text-green-400 p-4 rounded font-mono text-sm h-96 overflow-y-auto'>
            {testResults.length === 0 ? (
              <div className='text-gray-500'>No test results yet. Run some tests above.</div>
            ) : (
              testResults.map((result, index) => (
                <div key={index} className='mb-1'>
                  {result}
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
