/**
 * Zod Validation Schemas for School Management System
 *
 * Clean, focused validation schemas that match the simplified types
 * Designed for form validation and API request/response validation
 */

import { z } from 'zod';

// Common validation patterns
const emailSchema = z.string().email('Please enter a valid email address');
const phoneSchema = z.string().min(10, 'Please enter a valid phone number');

// User and Authentication schemas
export const UserRoleSchema = z.enum([
  'SUPER_ADMIN',
  'ADMIN',
  'STAFF',
  'TEACHER',
  'STUDENT',
  'PARENT',
]);

export const LoginSchema = z.object({
  email: emailSchema,
  password: z
    .string()
    .min(1, 'Password is required')
    .min(6, 'Password must be at least 6 characters'),
});

export const RegisterSchema = z
  .object({
    first_name: z
      .string()
      .min(1, 'First name is required')
      .min(2, 'First name must be at least 2 characters')
      .max(50, 'First name must be less than 50 characters'),
    last_name: z
      .string()
      .min(1, 'Last name is required')
      .min(2, 'Last name must be at least 2 characters')
      .max(50, 'Last name must be less than 50 characters'),
    email: emailSchema,
    password: z
      .string()
      .min(1, 'Password is required')
      .min(8, 'Password must be at least 8 characters')
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
        'Password must contain at least one uppercase letter, one lowercase letter, and one number'
      ),
    confirm_password: z.string().min(1, 'Please confirm your password'),
    role: UserRoleSchema.optional(),
  })
  .refine(data => data.password === data.confirm_password, {
    message: "Passwords don't match",
    path: ['confirm_password'],
  });

export const ForgotPasswordSchema = z.object({
  email: emailSchema,
});

export const ResetPasswordSchema = z
  .object({
    token: z.string().min(1, 'Reset token is required'),
    password: z
      .string()
      .min(1, 'Password is required')
      .min(8, 'Password must be at least 8 characters')
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
        'Password must contain at least one uppercase letter, one lowercase letter, and one number'
      ),
    confirm_password: z.string().min(1, 'Please confirm your password'),
  })
  .refine(data => data.password === data.confirm_password, {
    message: "Passwords don't match",
    path: ['confirm_password'],
  });

export const ChangePasswordSchema = z
  .object({
    current_password: z.string().min(1, 'Current password is required'),
    new_password: z
      .string()
      .min(1, 'New password is required')
      .min(8, 'Password must be at least 8 characters')
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
        'Password must contain at least one uppercase letter, one lowercase letter, and one number'
      ),
    confirm_password: z.string().min(1, 'Please confirm your new password'),
  })
  .refine(data => data.new_password === data.confirm_password, {
    message: "Passwords don't match",
    path: ['confirm_password'],
  });

export const UserSchema = z.object({
  id: z.string(),
  email: emailSchema,
  first_name: z.string().min(1, 'First name is required'),
  last_name: z.string().min(1, 'Last name is required'),
  role: UserRoleSchema,
  is_active: z.boolean(),
  profile_picture: z.string().optional(),
});

// Teacher schemas
export const TeacherSchema = z.object({
  id: z.string(),
  name: z.string().min(2, 'Name must be at least 2 characters'),
  subject: z.string().min(1, 'Subject is required'),
  email: emailSchema.optional(),
  department: z.string().optional(),
  phone: phoneSchema.optional(),
  status: z.enum(['ACTIVE', 'INACTIVE']).optional(),
  hire_date: z.string().optional(),
});

// Student schemas
export const StudentSchema = z.object({
  id: z.string(),
  reg_no: z.string().min(1, 'Registration number is required'),
  first_name: z.string().min(1, 'First name is required'),
  last_name: z.string().min(1, 'Last name is required'),
  gender: z.enum(['male', 'female', 'other']),
  dob: z.string().optional(),
  class_id: z.string().min(1, 'Class ID is required'),
  section_id: z.string().min(1, 'Section ID is required'),
  guardian_name: z.string().optional(),
  guardian_phone: z.string().optional(),
  address: z.string().optional(),
  email: emailSchema.optional(),
  photo_url: z.string().optional(),
  is_active: z.boolean().default(true),
  created_at: z.string(),
});

// Derived forms - single source of truth
export const StudentCreateSchema = StudentSchema.omit({ 
  id: true, 
  created_at: true, 
  photo_url: true, 
  is_active: true 
}).extend({
  password: z.string().min(8, 'Password must be at least 8 characters'),
  parent_id: z.string().min(1, 'Parent ID is required'),
});

export const StudentUpdateSchema = StudentSchema.partial().omit({ 
  id: true, 
  created_at: true 
});

export const StudentListResponseSchema = z.object({
  items: z.array(StudentSchema),
  page: z.number(),
  size: z.number(),
  total: z.number(),
  pages: z.number(),
});

export const StudentToggleResponseSchema = z.object({
  id: z.string(),
  is_active: z.boolean(),
});

export const StudentPhotoResponseSchema = z.object({
  photo_url: z.string(),
});

export const StudentImportResponseSchema = z.object({
  created: z.number(),
  updated: z.number(),
  errors: z.array(z.object({
    row: z.number(),
    error: z.string(),
  })),
});

// Class schemas
export const ClassSchema = z.object({
  id: z.string(),
  name: z.string().min(1, 'Class name is required'),
  grade_level: z.string().min(1, 'Grade level is required'),
  capacity: z.number().min(1, 'Capacity must be at least 1'),
  current_enrollment: z.number().optional(),
  teacher_name: z.string().optional(),
  room_number: z.string().optional(),
  is_active: z.boolean().optional(),
});

// Subject schemas
export const SubjectSchema = z.object({
  id: z.string(),
  name: z.string().min(1, 'Subject name is required'),
  code: z.string().min(1, 'Subject code is required'),
  category: z.string().optional(),
  credits: z.number().min(0, 'Credits must be non-negative').optional(),
  description: z.string().optional(),
  is_active: z.boolean().optional(),
});

// Attendance schemas
export const AttendanceSchema = z.object({
  id: z.string(),
  student_id: z.string().min(1, 'Student ID is required'),
  student_name: z.string().optional(),
  class_id: z.string().min(1, 'Class ID is required'),
  class_name: z.string().optional(),
  date: z.string().min(1, 'Date is required'),
  status: z.enum(['PRESENT', 'ABSENT', 'LATE', 'EXCUSED']),
  remarks: z.string().optional(),
});

// Exam schemas
export const ExamSchema = z.object({
  id: z.string(),
  title: z.string().min(1, 'Exam title is required'),
  subject_id: z.string().min(1, 'Subject is required'),
  subject_name: z.string().optional(),
  class_id: z.string().min(1, 'Class is required'),
  class_name: z.string().optional(),
  exam_date: z.string().min(1, 'Exam date is required'),
  total_marks: z.number().min(1, 'Total marks must be greater than 0'),
  passing_marks: z.number().min(0, 'Passing marks must be non-negative'),
  status: z.enum(['SCHEDULED', 'COMPLETED', 'CANCELLED']).optional(),
});

// Fee schemas
export const FeeSchema = z.object({
  id: z.string(),
  student_id: z.string().min(1, 'Student ID is required'),
  student_name: z.string().optional(),
  fee_type: z.string().min(1, 'Fee type is required'),
  amount: z.number().min(0, 'Amount must be non-negative'),
  due_date: z.string().min(1, 'Due date is required'),
  status: z.enum(['PENDING', 'PAID', 'OVERDUE', 'CANCELLED']),
  paid_amount: z.number().optional(),
  paid_date: z.string().optional(),
});

// Parent schemas
export const ParentSchema = z.object({
  id: z.string(),
  first_name: z.string().min(1, 'First name is required'),
  last_name: z.string().min(1, 'Last name is required'),
  email: emailSchema,
  phone: phoneSchema,
  relationship: z.enum(['FATHER', 'MOTHER', 'GUARDIAN']),
  occupation: z.string().optional(),
  address: z.string().optional(),
});

// Announcement schemas
export const AnnouncementSchema = z.object({
  id: z.string(),
  title: z.string().min(1, 'Title is required'),
  content: z.string().min(1, 'Content is required'),
  author_name: z.string().optional(),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']),
  target_audience: z.array(z.string()),
  publish_date: z.string().min(1, 'Publish date is required'),
  is_published: z.boolean(),
});

// Event schemas
export const EventSchema = z.object({
  id: z.string(),
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  event_date: z.string().min(1, 'Event date is required'),
  start_time: z.string().min(1, 'Start time is required'),
  end_time: z.string().min(1, 'End time is required'),
  location: z.string().optional(),
  organizer_name: z.string().optional(),
  is_public: z.boolean(),
});

// Grade schemas
export const GradeSchema = z.object({
  id: z.string(),
  student_id: z.string().min(1, 'Student ID is required'),
  student_name: z.string().optional(),
  exam_id: z.string().min(1, 'Exam ID is required'),
  exam_title: z.string().optional(),
  marks_obtained: z.number().min(0, 'Marks must be non-negative'),
  total_marks: z.number().min(1, 'Total marks must be greater than 0'),
  grade_letter: z.string().min(1, 'Grade letter is required'),
  percentage: z.number().min(0).max(100, 'Percentage must be between 0 and 100'),
});

// Form validation schemas for create/update operations
export const TeacherCreateSchema = TeacherSchema.omit({ id: true });
export const TeacherUpdateSchema = TeacherSchema.partial().omit({ id: true });

export const ClassCreateSchema = ClassSchema.omit({ id: true, current_enrollment: true });
export const ClassUpdateSchema = ClassSchema.partial().omit({ id: true });

export const SubjectCreateSchema = SubjectSchema.omit({ id: true });
export const SubjectUpdateSchema = SubjectSchema.partial().omit({ id: true });

export const AttendanceCreateSchema = AttendanceSchema.omit({
  id: true,
  student_name: true,
  class_name: true,
});
export const AttendanceUpdateSchema = z.object({
  status: z.enum(['PRESENT', 'ABSENT', 'LATE', 'EXCUSED']).optional(),
  remarks: z.string().optional(),
});

export const ExamCreateSchema = ExamSchema.omit({
  id: true,
  subject_name: true,
  class_name: true,
  status: true,
});
export const ExamUpdateSchema = ExamSchema.partial().omit({
  id: true,
  subject_id: true,
  class_id: true,
});

export const FeeCreateSchema = FeeSchema.omit({
  id: true,
  student_name: true,
  paid_amount: true,
  paid_date: true,
});
export const FeeUpdateSchema = FeeSchema.partial().omit({ id: true, student_id: true });

export const ParentCreateSchema = ParentSchema.omit({ id: true });
export const ParentUpdateSchema = ParentSchema.partial().omit({ id: true });

export const AnnouncementCreateSchema = AnnouncementSchema.omit({ id: true, author_name: true });
export const AnnouncementUpdateSchema = AnnouncementSchema.partial().omit({ id: true });

export const EventCreateSchema = EventSchema.omit({ id: true, organizer_name: true });
export const EventUpdateSchema = EventSchema.partial().omit({ id: true });

export const GradeCreateSchema = GradeSchema.omit({
  id: true,
  student_name: true,
  exam_title: true,
  grade_letter: true,
  percentage: true,
});
export const GradeUpdateSchema = z.object({
  marks_obtained: z.number().min(0, 'Marks must be non-negative').optional(),
});

// API Response schemas
export const ApiResponseSchema = <T extends z.ZodTypeAny>(dataSchema: T) =>
  z.object({
    data: dataSchema,
    message: z.string().optional(),
    success: z.boolean(),
  });

export const PaginatedResponseSchema = <T extends z.ZodTypeAny>(itemSchema: T) =>
  z.object({
    data: z.array(itemSchema),
    total: z.number(),
    page: z.number(),
    size: z.number(),
  });

// Authentication response schemas
export const AuthResponseSchema = z.object({
  access_token: z.string(),
  token_type: z.string().default('Bearer'),
  expires_in: z.number().optional(),
  user: UserSchema,
});

export const ErrorResponseSchema = z.object({
  message: z.string(),
  errors: z.record(z.array(z.string())).optional(),
  status_code: z.number().optional(),
});

// Export authentication types
export type LoginFormData = z.infer<typeof LoginSchema>;
export type RegisterFormData = z.infer<typeof RegisterSchema>;
export type ForgotPasswordFormData = z.infer<typeof ForgotPasswordSchema>;
export type ResetPasswordFormData = z.infer<typeof ResetPasswordSchema>;
export type ChangePasswordFormData = z.infer<typeof ChangePasswordSchema>;
export type AuthResponse = z.infer<typeof AuthResponseSchema>;
export type ErrorResponse = z.infer<typeof ErrorResponseSchema>;

// Export all other types
export type UserRole = z.infer<typeof UserRoleSchema>;
export type User = z.infer<typeof UserSchema>;
export type Teacher = z.infer<typeof TeacherSchema>;
export type Student = z.infer<typeof StudentSchema>;
export type StudentCreate = z.infer<typeof StudentCreateSchema>;
export type StudentUpdate = z.infer<typeof StudentUpdateSchema>;
export type StudentListResponse = z.infer<typeof StudentListResponseSchema>;
export type StudentToggleResponse = z.infer<typeof StudentToggleResponseSchema>;
export type StudentPhotoResponse = z.infer<typeof StudentPhotoResponseSchema>;
export type StudentImportResponse = z.infer<typeof StudentImportResponseSchema>;
export type Class = z.infer<typeof ClassSchema>;
export type Subject = z.infer<typeof SubjectSchema>;
export type Attendance = z.infer<typeof AttendanceSchema>;
export type Exam = z.infer<typeof ExamSchema>;
export type Fee = z.infer<typeof FeeSchema>;
export type Parent = z.infer<typeof ParentSchema>;
export type Announcement = z.infer<typeof AnnouncementSchema>;
export type Event = z.infer<typeof EventSchema>;
export type Grade = z.infer<typeof GradeSchema>;

// Create/Update types
export type TeacherCreate = z.infer<typeof TeacherCreateSchema>;
export type TeacherUpdate = z.infer<typeof TeacherUpdateSchema>;
export type ClassCreate = z.infer<typeof ClassCreateSchema>;
export type ClassUpdate = z.infer<typeof ClassUpdateSchema>;
export type SubjectCreate = z.infer<typeof SubjectCreateSchema>;
export type SubjectUpdate = z.infer<typeof SubjectUpdateSchema>;
export type AttendanceCreate = z.infer<typeof AttendanceCreateSchema>;
export type AttendanceUpdate = z.infer<typeof AttendanceUpdateSchema>;
export type ExamCreate = z.infer<typeof ExamCreateSchema>;
export type ExamUpdate = z.infer<typeof ExamUpdateSchema>;
export type FeeCreate = z.infer<typeof FeeCreateSchema>;
export type FeeUpdate = z.infer<typeof FeeUpdateSchema>;
export type ParentCreate = z.infer<typeof ParentCreateSchema>;
export type ParentUpdate = z.infer<typeof ParentUpdateSchema>;
export type AnnouncementCreate = z.infer<typeof AnnouncementCreateSchema>;
export type AnnouncementUpdate = z.infer<typeof AnnouncementUpdateSchema>;
export type EventCreate = z.infer<typeof EventCreateSchema>;
export type EventUpdate = z.infer<typeof EventUpdateSchema>;
export type GradeCreate = z.infer<typeof GradeCreateSchema>;
export type GradeUpdate = z.infer<typeof GradeUpdateSchema>;
