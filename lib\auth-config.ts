/**
 * Auth Configuration and Namespace Management
 * 
 * Handles the two possible backend namespaces:
 * - Mode A: /api/v1/auth/* 
 * - Mode B: /api/v1/users/auth/*
 */

// Get the auth namespace from environment
const AUTH_NAMESPACE = process.env.NEXT_PUBLIC_AUTH_NAMESPACE || 'users';
const API_BASE = process.env.NEXT_PUBLIC_API_BASE || 'http://localhost:8000';

export const AUTH_CONFIG = {
  namespace: AUTH_NAMESPACE,
  apiBase: API_BASE,
} as const;

/**
 * Build API path based on the configured namespace
 */
export function buildAuthPath(endpoint: string): string {
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
  
  if (AUTH_NAMESPACE === 'auth') {
    return `/api/v1/auth/${cleanEndpoint}`;
  } else {
    return `/api/v1/users/auth/${cleanEndpoint}`;
  }
}

/**
 * Build admin users path (always under users namespace)
 */
export function buildUsersPath(endpoint: string): string {
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
  return `/api/v1/users/admin/${cleanEndpoint}`;
}

/**
 * Get full backend URL for a path
 */
export function getBackendUrl(path: string): string {
  const cleanPath = path.startsWith('/') ? path : `/${path}`;
  return `${API_BASE}${cleanPath}`;
}

/**
 * Auth endpoints configuration
 */
export const AUTH_ENDPOINTS = {
  login: buildAuthPath('login'),
  logout: buildAuthPath('logout'),
  me: buildAuthPath('me'),
  updateMe: buildAuthPath('me'),
  changePassword: buildAuthPath('change-password'),
  register: buildAuthPath('register'), // Only available in users namespace
} as const;

/**
 * Users admin endpoints configuration
 */
export const USERS_ENDPOINTS = {
  list: buildUsersPath('users'),
  get: (id: string) => buildUsersPath(`users/${id}`),
  update: (id: string) => buildUsersPath(`users/${id}`),
  deactivate: (id: string) => buildUsersPath(`users/${id}/deactivate`),
  activate: (id: string) => buildUsersPath(`users/${id}/activate`),
  stats: buildUsersPath('stats/users'),
} as const;

/**
 * Detect which namespace is available by testing endpoints
 */
export async function detectAuthNamespace(): Promise<'auth' | 'users' | null> {
  const testEndpoints = [
    { namespace: 'auth', url: getBackendUrl('/api/v1/auth/me') },
    { namespace: 'users', url: getBackendUrl('/api/v1/users/auth/me') },
  ];

  for (const { namespace, url } of testEndpoints) {
    try {
      const response = await fetch(url, { method: 'GET' });
      // We expect 401 for unauthenticated requests, not 404
      if (response.status === 401) {
        return namespace as 'auth' | 'users';
      }
    } catch (error) {
      // Network errors are expected, continue testing
      continue;
    }
  }

  return null;
}

/**
 * Validate current namespace configuration
 */
export async function validateNamespaceConfig(): Promise<{
  isValid: boolean;
  detectedNamespace: 'auth' | 'users' | null;
  currentNamespace: string;
  suggestion?: string;
}> {
  const detectedNamespace = await detectAuthNamespace();
  const currentNamespace = AUTH_NAMESPACE;
  
  const isValid = detectedNamespace === currentNamespace;
  
  let suggestion: string | undefined;
  if (!isValid && detectedNamespace) {
    suggestion = `Set NEXT_PUBLIC_AUTH_NAMESPACE=${detectedNamespace} in your .env.local file`;
  } else if (!detectedNamespace) {
    suggestion = 'Backend may not be running or endpoints are not available';
  }

  return {
    isValid,
    detectedNamespace,
    currentNamespace,
    suggestion,
  };
}
