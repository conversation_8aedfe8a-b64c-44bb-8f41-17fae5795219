/**
 * API Client - Axios Configuration with Interceptors
 *
 * Features:
 * - Base axios client with interceptors
 * - Automatic token attachment
 * - Request/response logging
 * - Error handling and retry logic
 * - Loading state management
 * - Request/response transformations
 * - Environment-based configuration
 */

import axios, {
  AxiosError,
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  InternalAxiosRequestConfig,
} from 'axios';
import { toast } from 'sonner';

// Types
interface ApiError {
  message: string;
  code?: string;
  status?: number;
  details?: any;
}

interface RequestConfig extends AxiosRequestConfig {
  skipAuth?: boolean;
  skipErrorToast?: boolean;
  retryCount?: number;
  timeout?: number;
  metadata?: {
    startTime: number;
  };
}

// Configuration
const API_CONFIG = {
  baseURL: process.env.NEXT_PUBLIC_API_URL || '/api', // ✅ Use relative URL for Next.js rewrites
  timeout: 30000,
  retryAttempts: 3,
  retryDelay: 1000,
};

// Token management utilities
const tokenManager = {
  getToken: (): string | null => {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem('auth-token');
  },

  setToken: (token: string): void => {
    if (typeof window === 'undefined') return;
    localStorage.setItem('auth-token', token);
  },

  removeToken: (): void => {
    if (typeof window === 'undefined') return;
    localStorage.removeItem('auth-token');
  },

  isTokenExpired: (token: string): boolean => {
    try {
      const parts = token.split('.');
      if (parts.length !== 3) return true;
      const payload = JSON.parse(atob(parts[1]!));
      const currentTime = Date.now() / 1000;
      return payload.exp < currentTime;
    } catch {
      return true;
    }
  },
};

// Create axios instance
const apiClient: AxiosInstance = axios.create({
  baseURL: API_CONFIG.baseURL,
  timeout: API_CONFIG.timeout,
  headers: {
    'Content-Type': 'application/json',
    Accept: 'application/json',
  },
});

// Request interceptor
apiClient.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // Add timestamp for request tracking
    config.metadata = { startTime: Date.now() };

    // Add authentication token
    if (!config.skipAuth) {
      const token = tokenManager.getToken();
      if (token && !tokenManager.isTokenExpired(token)) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }

    // Add request ID for tracking
    config.headers['X-Request-ID'] = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Log request in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`, {
        headers: config.headers,
        data: config.data,
        params: config.params,
      });
    }

    return config;
  },
  (error: AxiosError) => {
    console.error('❌ Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    // Calculate request duration
    const duration = Date.now() - (response.config.metadata?.startTime || 0);

    // Log response in development
    if (process.env.NODE_ENV === 'development') {
      console.log(
        `✅ API Response: ${response.config.method?.toUpperCase()} ${
          response.config.url
        } (${duration}ms)`,
        {
          status: response.status,
          data: response.data,
          headers: response.headers,
        }
      );
    }

    // Transform response data if needed
    if (response.data && typeof response.data === 'object') {
      // Convert date strings to Date objects
      response.data = transformDates(response.data);
    }

    return response;
  },
  async (error: AxiosError) => {
    const originalRequest = error.config as RequestConfig & { _retry?: boolean };

    // Calculate request duration
    const duration = Date.now() - (originalRequest?.metadata?.startTime || 0);

    // Log error in development
    if (process.env.NODE_ENV === 'development') {
      console.error(
        `❌ API Error: ${originalRequest?.method?.toUpperCase()} ${
          originalRequest?.url
        } (${duration}ms)`,
        {
          status: error.response?.status,
          data: error.response?.data,
          message: error.message,
        }
      );
    }

    // Handle different error scenarios
    if (error.response) {
      const status = error.response.status;
      const data = error.response.data as any;

      switch (status) {
        case 401:
          // Unauthorized - try to refresh token
          if (!originalRequest._retry && !originalRequest.skipAuth) {
            originalRequest._retry = true;

            try {
              await refreshToken();
              // Retry original request with new token
              const token = tokenManager.getToken();
              if (token) {
                originalRequest.headers = originalRequest.headers || {};
                originalRequest.headers.Authorization = `Bearer ${token}`;
                return apiClient(originalRequest);
              }
            } catch (refreshError) {
              // Refresh failed, redirect to login
              handleAuthError();
              return Promise.reject(refreshError);
            }
          } else {
            handleAuthError();
          }
          break;

        case 403:
          // Forbidden
          if (!originalRequest.skipErrorToast) {
            toast.error("Access denied. You don't have permission to perform this action.");
          }
          break;

        case 404:
          // Not found
          if (!originalRequest.skipErrorToast) {
            toast.error('Resource not found.');
          }
          break;

        case 422:
          // Validation error
          if (!originalRequest.skipErrorToast) {
            const message = data?.message || 'Validation failed';
            toast.error(message);
          }
          break;

        case 429:
          // Rate limited - retry with exponential backoff
          if (
            !originalRequest._retry &&
            (originalRequest.retryCount || 0) < API_CONFIG.retryAttempts
          ) {
            originalRequest._retry = true;
            originalRequest.retryCount = (originalRequest.retryCount || 0) + 1;

            const delay = API_CONFIG.retryDelay * Math.pow(2, originalRequest.retryCount - 1);
            await new Promise(resolve => setTimeout(resolve, delay));

            return apiClient(originalRequest);
          }

          if (!originalRequest.skipErrorToast) {
            toast.error('Too many requests. Please try again later.');
          }
          break;

        case 500:
        case 502:
        case 503:
        case 504:
          // Server errors - retry with exponential backoff
          if (
            !originalRequest._retry &&
            (originalRequest.retryCount || 0) < API_CONFIG.retryAttempts
          ) {
            originalRequest._retry = true;
            originalRequest.retryCount = (originalRequest.retryCount || 0) + 1;

            const delay = API_CONFIG.retryDelay * Math.pow(2, originalRequest.retryCount - 1);
            await new Promise(resolve => setTimeout(resolve, delay));

            return apiClient(originalRequest);
          }

          if (!originalRequest.skipErrorToast) {
            toast.error('Server error. Please try again later.');
          }
          break;

        default:
          if (!originalRequest.skipErrorToast) {
            const message = data?.message || `Request failed with status ${status}`;
            toast.error(message);
          }
      }

      // Create structured error
      const apiError: ApiError = {
        message: data?.message || error.message || 'Request failed',
        code: data?.code,
        status: status,
        details: data?.details || data,
      };

      return Promise.reject(apiError);
    } else if (error.request) {
      // Network error
      if (!originalRequest?.skipErrorToast) {
        toast.error('Network error. Please check your connection.');
      }

      const apiError: ApiError = {
        message: 'Network error',
        code: 'NETWORK_ERROR',
      };

      return Promise.reject(apiError);
    } else {
      // Request setup error
      if (!originalRequest?.skipErrorToast) {
        toast.error('Request configuration error.');
      }

      const apiError: ApiError = {
        message: error.message || 'Request setup error',
        code: 'REQUEST_ERROR',
      };

      return Promise.reject(apiError);
    }
  }
);

// Utility functions
function transformDates(obj: any): any {
  if (obj === null || obj === undefined) return obj;

  if (typeof obj === 'string') {
    // Check if string is a date
    const dateRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/;
    if (dateRegex.test(obj)) {
      return new Date(obj);
    }
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(transformDates);
  }

  if (typeof obj === 'object') {
    const transformed: any = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        transformed[key] = transformDates(obj[key]);
      }
    }
    return transformed;
  }

  return obj;
}

async function refreshToken(): Promise<void> {
  const refreshToken = localStorage.getItem('refresh-token');
  if (!refreshToken) {
    throw new Error('No refresh token available');
  }

  try {
    const response = await axios.post(`${API_CONFIG.baseURL}/auth/refresh`, {
      refreshToken,
    });

    const { token, refreshToken: newRefreshToken } = response.data;

    tokenManager.setToken(token);
    if (newRefreshToken) {
      localStorage.setItem('refresh-token', newRefreshToken);
    }
  } catch (error) {
    tokenManager.removeToken();
    localStorage.removeItem('refresh-token');
    throw error;
  }
}

function handleAuthError(): void {
  tokenManager.removeToken();
  localStorage.removeItem('refresh-token');

  // Redirect to login page
  if (typeof window !== 'undefined') {
    const currentPath = window.location.pathname;
    if (currentPath !== '/login') {
      window.location.href = `/login?redirect=${encodeURIComponent(currentPath)}`;
    }
  }
}

// API methods with type safety
export const api = {
  // Generic methods
  get: <T = any>(url: string, config?: RequestConfig): Promise<AxiosResponse<T>> =>
    apiClient.get(url, config),

  post: <T = any>(url: string, data?: any, config?: RequestConfig): Promise<AxiosResponse<T>> =>
    apiClient.post(url, data, config),

  put: <T = any>(url: string, data?: any, config?: RequestConfig): Promise<AxiosResponse<T>> =>
    apiClient.put(url, data, config),

  patch: <T = any>(url: string, data?: any, config?: RequestConfig): Promise<AxiosResponse<T>> =>
    apiClient.patch(url, data, config),

  delete: <T = any>(url: string, config?: RequestConfig): Promise<AxiosResponse<T>> =>
    apiClient.delete(url, config),

  // File upload
  upload: <T = any>(url: string, file: File, config?: RequestConfig): Promise<AxiosResponse<T>> => {
    const formData = new FormData();
    formData.append('file', file);

    return apiClient.post(url, formData, {
      ...config,
      headers: {
        ...config?.headers,
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  // Download file
  download: (url: string, filename?: string, config?: RequestConfig): Promise<void> => {
    return apiClient
      .get(url, {
        ...config,
        responseType: 'blob',
      })
      .then(response => {
        const blob = new Blob([response.data]);
        const downloadUrl = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = filename || 'download';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(downloadUrl);
      });
  },
};

// Export the configured client
export { apiClient };
export default apiClient;

// Export types
export type { ApiError, RequestConfig };
