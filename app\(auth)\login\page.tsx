/**
 * Login Page - Professional implementation with Auth Store
 *
 * Features:
 * - Form handling with validation
 * - Auth store integration
 * - Dummy/Real API toggle
 * - Error handling and user feedback
 * - Redirect after successful login
 */

'use client';

import { useRouter } from 'next/navigation';
import { useState } from 'react';

import { useAuth } from '@/hooks/useAuth';

// Updated credentials to match API route
const DEMO_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'admin123',
};

export default function LoginPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const { setAuthData } = useAuth();
  const router = useRouter();

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      // 🚧 TEMPORARY MOCK AUTHENTICATION FOR DEVELOPMENT
      // TODO: Replace with real API call when connecting to FastAPI backend

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Mock credentials validation
      if (email === '<EMAIL>' && password === 'admin123') {
        // Create mock user data
        const mockUser = {
          id: '1',
          email: '<EMAIL>',
          name: 'School Administrator',
          role: 'ADMIN' as const,
          avatar: 'https://api.dicebear.com/7.x/initials/svg?seed=School Administrator',
          permissions: ['read', 'write', 'delete', 'admin'],
        };

        const mockToken = 'mock-jwt-token-' + Date.now();

        // Update auth store with mock data
        setAuthData(mockToken, mockUser);

        // Set mock cookie for middleware (optional, since middleware is bypassed)
        document.cookie = `auth-token=${mockToken}; path=/; max-age=86400`; // 24 hours

        console.log('🚧 DEV MODE: Mock login successful', { user: mockUser, token: mockToken });

        // Redirect to dashboard
        router.push('/dashboard');
      } else {
        throw new Error('Invalid credentials. Use <EMAIL> / admin123 for demo.');
      }
    } catch (err: any) {
      setError(err.message || 'Login failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='text-center'>
        <h2 className='text-2xl font-bold text-gray-900'>Sign in to your account</h2>
        <p className='mt-2 text-sm text-gray-600'>Access the school management system</p>
      </div>

      {/* Demo credentials info */}
      <div className='bg-yellow-50 border border-yellow-200 rounded-md p-4'>
        <div className='text-sm text-yellow-800'>
          <p className='font-medium mb-2'>Demo Credentials:</p>
          <p>Email: {DEMO_CREDENTIALS.email}</p>
          <p>Password: {DEMO_CREDENTIALS.password}</p>
        </div>
      </div>

      {/* Login Form */}
      <form className='space-y-6' onSubmit={handleLogin}>
        <div>
          <label htmlFor='email' className='block text-sm font-medium text-gray-700'>
            Email address
          </label>
          <div className='mt-1'>
            <input
              id='email'
              name='email'
              type='email'
              autoComplete='email'
              required
              value={email}
              onChange={e => setEmail(e.target.value)}
              className='appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'
              placeholder='Enter your email'
            />
          </div>
        </div>

        <div>
          <label htmlFor='password' className='block text-sm font-medium text-gray-700'>
            Password
          </label>
          <div className='mt-1'>
            <input
              id='password'
              name='password'
              type='password'
              autoComplete='current-password'
              required
              value={password}
              onChange={e => setPassword(e.target.value)}
              className='appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'
              placeholder='Enter your password'
            />
          </div>
        </div>

        <div className='flex items-center justify-between'>
          <div className='flex items-center'>
            <input
              id='remember-me'
              name='remember-me'
              type='checkbox'
              className='h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded'
            />
            <label htmlFor='remember-me' className='ml-2 block text-sm text-gray-900'>
              Remember me
            </label>
          </div>

          <div className='text-sm'>
            <a href='#' className='font-medium text-blue-600 hover:text-blue-500'>
              Forgot your password?
            </a>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className='bg-red-50 border border-red-200 rounded-md p-4'>
            <div className='text-sm text-red-800'>{error}</div>
          </div>
        )}

        {/* Submit Button */}
        <div>
          <button
            type='submit'
            disabled={isLoading}
            className='group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed'
          >
            {isLoading ? (
              <div className='flex items-center'>
                <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2' />
                Signing in...
              </div>
            ) : (
              'Sign in'
            )}
          </button>
        </div>

        {/* Development Mode Indicator */}
        <div className='text-center'>
          <span className='px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded'>
            Development Mode - Cookie-based Auth
          </span>
        </div>
      </form>

      {/* Additional links */}
      <div className='text-center text-sm text-gray-600'>
        <p>
          Don't have an account?{' '}
          <a href='#' className='font-medium text-blue-600 hover:text-blue-500'>
            Contact administrator
          </a>
        </p>
      </div>
    </div>
  );
}
