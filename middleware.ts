/**
 * Next.js Middleware - Route Protection and Management
 *
 * Features:
 * - Authentication-based route protection
 * - Role-based access control
 * - Automatic redirects for protected routes
 * - Request logging and analytics
 * - Rate limiting and security headers
 * - Internationalization support ready
 */

import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';

// Define route patterns
const PUBLIC_ROUTES = [
  '/',
  '/login',
  '/register',
  '/forgot-password',
  '/reset-password',
  '/terms',
  '/privacy',
  '/contact',
];

const AUTH_ROUTES = ['/login', '/register', '/forgot-password', '/reset-password'];

const PROTECTED_ROUTES = ['/dashboard'];

// Role-based route access
const ROLE_ROUTES = {
  SUPER_ADMIN: ['/dashboard', '/dashboard/settings', '/dashboard/users', '/dashboard/system'],
  ADMIN: [
    '/dashboard',
    '/dashboard/teachers',
    '/dashboard/students',
    '/dashboard/classes',
    '/dashboard/reports',
  ],
  TEACHER: [
    '/dashboard',
    '/dashboard/classes',
    '/dashboard/students',
    '/dashboard/attendance',
    '/dashboard/grades',
  ],
  STAFF: ['/dashboard', '/dashboard/students', '/dashboard/attendance'],
  STUDENT: ['/dashboard', '/dashboard/profile', '/dashboard/grades', '/dashboard/assignments'],
  PARENT: ['/dashboard', '/dashboard/profile', '/dashboard/children'],
} as const;

type UserRole = keyof typeof ROLE_ROUTES;

// Helper functions
function isPublicRoute(pathname: string): boolean {
  return PUBLIC_ROUTES.some(route => pathname === route || pathname.startsWith(`${route}/`));
}

function isAuthRoute(pathname: string): boolean {
  return AUTH_ROUTES.some(route => pathname === route || pathname.startsWith(`${route}/`));
}

function isProtectedRoute(pathname: string): boolean {
  return PROTECTED_ROUTES.some(route => pathname === route || pathname.startsWith(`${route}/`));
}

function hasRoleAccess(pathname: string, userRole: UserRole): boolean {
  const allowedRoutes = ROLE_ROUTES[userRole] || [];
  return allowedRoutes.some(route => pathname === route || pathname.startsWith(`${route}/`));
}

function getTokenFromRequest(request: NextRequest): string | null {
  // Try to get token from cookie first (most secure)
  const tokenFromCookie = request.cookies.get('auth-token')?.value;
  if (tokenFromCookie) {
    return tokenFromCookie;
  }

  // Fallback to Authorization header
  const authHeader = request.headers.get('authorization');
  if (authHeader?.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  return null;
}

function parseJWT(token: string): { exp?: number; role?: UserRole; userId?: string } | null {
  try {
    const tokenPart = token.split('.')[1];
    if (!tokenPart) {
      return null;
    }
    const payload = JSON.parse(atob(tokenPart));
    return payload;
  } catch {
    return null;
  }
}

function isTokenExpired(token: string): boolean {
  const payload = parseJWT(token);
  if (!payload?.exp) {
    return false;
  }

  const currentTime = Date.now() / 1000;
  return payload.exp < currentTime;
}

function addSecurityHeaders(response: NextResponse): NextResponse {
  // Security headers
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  response.headers.set('X-XSS-Protection', '1; mode=block');

  // CSP header for enhanced security
  response.headers.set(
    'Content-Security-Policy',
    "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:;"
  );

  return response;
}

function logRequest(request: NextRequest, action: string, details?: string): void {
  if (process.env.NODE_ENV === 'development') {
    const timestamp = new Date().toISOString();
    const ip = request.ip || request.headers.get('x-forwarded-for') || 'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    console.log(`[${timestamp}] ${action} - ${request.method} ${request.nextUrl.pathname}`);
    console.log(`  IP: ${ip}`);
    console.log(`  User-Agent: ${userAgent.substring(0, 100)}...`);
    if (details) {
      console.log(`  Details: ${details}`);
    }
  }
}

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Skip middleware for static files and API routes
  if (
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/api/') ||
    pathname.includes('.') // Static files
  ) {
    return NextResponse.next();
  }

  // 🚧 TEMPORARY DEVELOPMENT MODE - BYPASS ALL AUTH CHECKS
  // TODO: Re-enable full authentication when connecting to real FastAPI backend

  logRequest(request, 'DEV_MODE_BYPASS', 'All authentication checks disabled for development');

  // Only handle basic redirects for development UX
  if (pathname === '/') {
    // Redirect root to dashboard for easier development
    logRequest(request, 'DEV_REDIRECT', 'Root to Dashboard');
    return NextResponse.redirect(new URL('/dashboard', request.url));
  }

  // Allow all routes unconditionally during development
  logRequest(request, 'DEV_ALLOW_ALL', `Allowing access to ${pathname}`);
  return addSecurityHeaders(NextResponse.next());
}

/*
🔒 PRODUCTION MIDDLEWARE (COMMENTED OUT FOR DEVELOPMENT)
TODO: Uncomment and use this when integrating real FastAPI backend

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Skip middleware for static files and API routes
  if (
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/api/') ||
    pathname.includes('.') // Static files
  ) {
    return NextResponse.next();
  }

  logRequest(request, 'MIDDLEWARE_START');

  // Get authentication token
  const token = getTokenFromRequest(request);
  const isAuthenticated = token && !isTokenExpired(token);
  const userPayload = token ? parseJWT(token) : null;
  const userRole = userPayload?.role;

  // Handle root path redirect
  if (pathname === '/') {
    if (isAuthenticated) {
      logRequest(request, 'REDIRECT_TO_DASHBOARD', 'Authenticated user accessing root');
      return NextResponse.redirect(new URL('/dashboard', request.url));
    } else {
      logRequest(request, 'REDIRECT_TO_LOGIN', 'Unauthenticated user accessing root');
      return NextResponse.redirect(new URL('/login', request.url));
    }
  }

  // Handle authentication routes
  if (isAuthRoute(pathname)) {
    if (isAuthenticated) {
      logRequest(request, 'REDIRECT_FROM_AUTH', 'Authenticated user accessing auth route');
      return NextResponse.redirect(new URL('/dashboard', request.url));
    }

    // Allow access to auth routes for unauthenticated users
    logRequest(request, 'ALLOW_AUTH_ROUTE');
    return addSecurityHeaders(NextResponse.next());
  }

  // Handle public routes
  if (isPublicRoute(pathname)) {
    logRequest(request, 'ALLOW_PUBLIC_ROUTE');
    return addSecurityHeaders(NextResponse.next());
  }

  // Handle protected routes
  if (isProtectedRoute(pathname)) {
    if (!isAuthenticated) {
      logRequest(request, 'REDIRECT_TO_LOGIN', 'Unauthenticated access to protected route');
      const loginUrl = new URL('/login', request.url);
      loginUrl.searchParams.set('redirect', pathname);
      return NextResponse.redirect(loginUrl);
    }

    // Check role-based access
    if (userRole && !hasRoleAccess(pathname, userRole)) {
      logRequest(request, 'ACCESS_DENIED', `Role ${userRole} denied access to ${pathname}`);
      return NextResponse.redirect(new URL('/dashboard?error=access-denied', request.url));
    }

    logRequest(request, 'ALLOW_PROTECTED_ROUTE', `Role: ${userRole}`);

    // Add user info to headers for the application
    const response = NextResponse.next();
    if (userPayload) {
      response.headers.set('x-user-id', userPayload.userId || '');
      response.headers.set('x-user-role', userPayload.role || '');
    }

    return addSecurityHeaders(response);
  }

  // Default: allow access but add security headers
  logRequest(request, 'ALLOW_DEFAULT');
  return addSecurityHeaders(NextResponse.next());
}
*/

// Configure which routes the middleware should run on
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
};

// Export types for use in other files
export { AUTH_ROUTES, PROTECTED_ROUTES, PUBLIC_ROUTES, ROLE_ROUTES };
export type { UserRole };
