'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2, Save, User, X } from 'lucide-react';
import { useForm } from 'react-hook-form';

// Schema imports - demonstrating strict type usage
import {
  StudentCreateSchema,
  StudentUpdateSchema,
  type StudentCreate,
  type StudentUpdate,
} from '@/schemas/zodSchemas';

// UI Components
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormSection,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

// Types for the component props - strict typing
interface StudentFormProps {
  mode: 'create' | 'edit';
  initialData?: StudentUpdate;
  onSubmit: (data: StudentCreate | StudentUpdate) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

// Grade levels - type-safe options
const GRADE_LEVELS = [
  'Kindergarten',
  '1st Grade',
  '2nd Grade',
  '3rd Grade',
  '4th Grade',
  '5th Grade',
  '6th Grade',
  '7th Grade',
  '8th Grade',
  '9th Grade',
  '10th Grade',
  '11th Grade',
  '12th Grade',
] as const;

// Class names - could come from API
const CLASS_NAMES = [
  'Class A',
  'Class B',
  'Class C',
  'Class D',
  'Advanced',
  'Honors',
  'Regular',
] as const;

const STATUS_OPTIONS = [
  { value: 'ACTIVE', label: 'Active' },
  { value: 'INACTIVE', label: 'Inactive' },
  { value: 'GRADUATED', label: 'Graduated' },
] as const;

/**
 * StudentForm Component
 *
 * Demonstrates strict type usage with Zod + React Hook Form:
 * - Uses zodResolver for comprehensive validation
 * - Types inferred from Zod schemas
 * - Complex validation rules (email, phone, student ID)
 * - Real-time validation with proper error display
 * - Type-safe form submission
 */
export function StudentForm({
  mode,
  initialData,
  onSubmit,
  onCancel,
  isLoading = false,
}: StudentFormProps) {
  // Schema selection based on mode - demonstrating conditional schema usage
  const schema = mode === 'create' ? StudentCreateSchema : StudentUpdateSchema;

  // Form initialization with strict typing
  const form = useForm<StudentCreate | StudentUpdate>({
    resolver: zodResolver(schema),
    defaultValues:
      mode === 'create'
        ? {
            student_id: '',
            first_name: '',
            last_name: '',
            email: '',
            phone: '',
            class_name: '',
            grade_level: '',
            status: 'ACTIVE',
            enrollment_date: new Date().toISOString().split('T')[0],
          }
        : initialData || {},
    mode: 'onChange', // Real-time validation
  });

  // Type-safe form submission with validation
  const handleSubmit = async (data: StudentCreate | StudentUpdate) => {
    try {
      // Validate data against schema before submission
      const validatedData = schema.parse(data);
      await onSubmit(validatedData);
    } catch (error) {
      console.error('Form validation error:', error);
      // Handle validation errors if needed
    }
  };

  return (
    <Card className='w-full max-w-3xl mx-auto'>
      <CardHeader>
        <CardTitle className='flex items-center gap-2'>
          <User className='w-5 h-5' />
          {mode === 'create' ? 'Add New Student' : 'Edit Student'}
        </CardTitle>
        <CardDescription>
          {mode === 'create'
            ? 'Fill in the details to register a new student in the system.'
            : 'Update the student information below.'}
        </CardDescription>
      </CardHeader>

      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className='space-y-6'>
            {/* Personal Information Section */}
            <FormSection
              title='Personal Information'
              description='Basic student details and contact information'
            >
              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                {/* Student ID Field - Required, unique identifier */}
                <FormField
                  control={form.control}
                  name='student_id'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Student ID *</FormLabel>
                      <FormControl>
                        <Input placeholder='e.g., STU2024001' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Status Field - Enum validation */}
                <FormField
                  control={form.control}
                  name='status'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        {...(field.value && { defaultValue: field.value })}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder='Select status' />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {STATUS_OPTIONS.map(option => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* First Name Field - Required */}
                <FormField
                  control={form.control}
                  name='first_name'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>First Name *</FormLabel>
                      <FormControl>
                        <Input placeholder='Enter first name' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Last Name Field - Required */}
                <FormField
                  control={form.control}
                  name='last_name'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Last Name *</FormLabel>
                      <FormControl>
                        <Input placeholder='Enter last name' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Email Field - Required with validation */}
                <FormField
                  control={form.control}
                  name='email'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email Address *</FormLabel>
                      <FormControl>
                        <Input type='email' placeholder='<EMAIL>' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Phone Field - Optional but validated */}
                <FormField
                  control={form.control}
                  name='phone'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone Number</FormLabel>
                      <FormControl>
                        <Input type='tel' placeholder='+****************' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </FormSection>

            {/* Academic Information Section */}
            <FormSection
              title='Academic Information'
              description='Class assignment and enrollment details'
            >
              <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
                {/* Grade Level Field - Optional */}
                <FormField
                  control={form.control}
                  name='grade_level'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Grade Level</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        {...(field.value && { defaultValue: field.value })}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder='Select grade' />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {GRADE_LEVELS.map(grade => (
                            <SelectItem key={grade} value={grade}>
                              {grade}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Class Name Field - Optional */}
                <FormField
                  control={form.control}
                  name='class_name'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Class</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        {...(field.value && { defaultValue: field.value })}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder='Select class' />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {CLASS_NAMES.map(className => (
                            <SelectItem key={className} value={className}>
                              {className}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Enrollment Date Field - Optional */}
                <FormField
                  control={form.control}
                  name='enrollment_date'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Enrollment Date</FormLabel>
                      <FormControl>
                        <Input type='date' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </FormSection>

            {/* Form Actions */}
            <div className='flex justify-end space-x-4 pt-6 border-t'>
              <Button type='button' variant='outline' onClick={onCancel} disabled={isLoading}>
                <X className='w-4 h-4 mr-2' />
                Cancel
              </Button>

              <Button type='submit' disabled={isLoading || !form.formState.isValid}>
                {isLoading ? (
                  <Loader2 className='w-4 h-4 mr-2 animate-spin' />
                ) : (
                  <Save className='w-4 h-4 mr-2' />
                )}
                {mode === 'create' ? 'Register Student' : 'Update Student'}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}

// Export type for external usage
export type { StudentFormProps };

// Default export for lazy loading
export default StudentForm;
